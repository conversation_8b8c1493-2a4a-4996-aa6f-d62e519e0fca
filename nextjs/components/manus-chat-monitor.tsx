"use client"

import React, { useState, useEffect, useRef } from 'react';
import { useWebSocket } from '@/contexts/WebSocketContext';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import MarkdownRenderer from "@/components/markdown-renderer";
import {
  AlertCircle,
  Clock,
  CheckCircle,
  XCircle,
  RefreshCcw,
  Send,
  User,
  <PERSON><PERSON>,
  <PERSON>s,
  Loader2,
  ChevronDown,
  ChevronRight,
  FileText,
  Code,
  Copy,
  Download,
  Eye,
  Calendar
} from "lucide-react";

// Import ChatMessage type from WebSocketContext
import { ChatMessage as WebSocketChatMessage } from '@/contexts/WebSocketContext';

// Define message type with additional status field
interface ChatMessage extends WebSocketChatMessage {
  status?: 'sending' | 'sent' | 'error';
  content_type?: 'text' | 'plan' | 'code' | 'file_attachment';
  plan_data?: {
    steps: Array<{
      title: string;
      description: string;
      status: 'pending' | 'in_progress' | 'completed';
      substeps?: Array<{
        title: string;
        status: 'pending' | 'in_progress' | 'completed';
      }>;
    }>;
  };
  code_data?: {
    language: string;
    code: string;
    filename?: string;
  };
  file_data?: {
    filename: string;
    size: string;
    type: 'code' | 'document' | 'image' | 'other';
    url?: string;
  };
}

// Component for rendering formatted text with lists
const FormattedTextRenderer: React.FC<{ content: string }> = ({ content }) => {
  const renderFormattedText = (text: string) => {
    const lines = text.split('\n');
    const elements: React.ReactNode[] = [];
    let currentList: React.ReactNode[] = [];
    let listType: 'ordered' | 'unordered' | null = null;

    const flushList = () => {
      if (currentList.length > 0) {
        if (listType === 'ordered') {
          elements.push(<ol key={elements.length} className="list-decimal list-inside space-y-1 ml-4">{currentList}</ol>);
        } else if (listType === 'unordered') {
          elements.push(<ul key={elements.length} className="list-disc list-inside space-y-1 ml-4">{currentList}</ul>);
        }
        currentList = [];
        listType = null;
      }
    };

    lines.forEach((line, index) => {
      const trimmedLine = line.trim();

      // Check for numbered list
      const numberedMatch = trimmedLine.match(/^(\d+)\.\s+(.+)$/);
      if (numberedMatch) {
        if (listType !== 'ordered') {
          flushList();
          listType = 'ordered';
        }
        currentList.push(
          <li key={index} className="text-gray-800 dark:text-gray-200">
            <span className="font-semibold">{numberedMatch[2]}</span>
          </li>
        );
        return;
      }

      // Check for bullet list
      const bulletMatch = trimmedLine.match(/^[•·]\s+(.+)$/);
      if (bulletMatch) {
        if (listType !== 'unordered') {
          flushList();
          listType = 'unordered';
        }
        currentList.push(
          <li key={index} className="text-gray-700 dark:text-gray-300">
            {bulletMatch[1]}
          </li>
        );
        return;
      }

      // Regular text
      flushList();
      if (trimmedLine) {
        elements.push(
          <p key={index} className="text-gray-800 dark:text-gray-200 leading-relaxed">
            {trimmedLine}
          </p>
        );
      }
    });

    flushList();
    return elements;
  };

  return <div className="space-y-3">{renderFormattedText(content)}</div>;
};

// Component for rendering plan mode
const PlanRenderer: React.FC<{ planData: ChatMessage['plan_data'] }> = ({ planData }) => {
  const [expandedSteps, setExpandedSteps] = useState<Set<number>>(new Set());

  const toggleStep = (stepIndex: number) => {
    const newExpanded = new Set(expandedSteps);
    if (newExpanded.has(stepIndex)) {
      newExpanded.delete(stepIndex);
    } else {
      newExpanded.add(stepIndex);
    }
    setExpandedSteps(newExpanded);
  };

  if (!planData?.steps) return null;

  return (
    <div className="space-y-3">
      {planData.steps.map((step, index) => (
        <div key={index} className="border border-gray-200 dark:border-gray-700 rounded-lg">
          <div
            className="flex items-center gap-3 p-3 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-800"
            onClick={() => toggleStep(index)}
          >
            <div className="flex-shrink-0">
              {step.status === 'completed' ? (
                <CheckCircle className="h-5 w-5 text-green-500" />
              ) : step.status === 'in_progress' ? (
                <Loader2 className="h-5 w-5 text-blue-500 animate-spin" />
              ) : (
                <div className="h-5 w-5 rounded-full border-2 border-gray-300 dark:border-gray-600" />
              )}
            </div>
            <div className="flex-1">
              <h4 className="font-medium text-gray-900 dark:text-gray-100">{step.title}</h4>
              {step.description && (
                <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">{step.description}</p>
              )}
            </div>
            <div className="flex-shrink-0">
              {expandedSteps.has(index) ? (
                <ChevronDown className="h-4 w-4 text-gray-500" />
              ) : (
                <ChevronRight className="h-4 w-4 text-gray-500" />
              )}
            </div>
          </div>

          {expandedSteps.has(index) && step.substeps && (
            <div className="px-6 pb-3 border-t border-gray-100 dark:border-gray-700">
              <div className="space-y-2 mt-3">
                {step.substeps.map((substep, subIndex) => (
                  <div key={subIndex} className="flex items-center gap-3">
                    <div className="flex-shrink-0 ml-4">
                      {substep.status === 'completed' ? (
                        <CheckCircle className="h-4 w-4 text-green-500" />
                      ) : substep.status === 'in_progress' ? (
                        <Loader2 className="h-4 w-4 text-blue-500 animate-spin" />
                      ) : (
                        <div className="h-4 w-4 rounded-full border-2 border-gray-300 dark:border-gray-600" />
                      )}
                    </div>
                    <span className="text-sm text-gray-700 dark:text-gray-300">{substep.title}</span>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      ))}
    </div>
  );
};

// Component for rendering code blocks
const CodeRenderer: React.FC<{ codeData: ChatMessage['code_data'] }> = ({ codeData }) => {
  const [copied, setCopied] = useState(false);

  const copyToClipboard = async () => {
    if (codeData?.code) {
      await navigator.clipboard.writeText(codeData.code);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    }
  };

  if (!codeData?.code) return null;

  return (
    <div className="bg-gray-900 dark:bg-gray-950 rounded-lg overflow-hidden border border-gray-700">
      <div className="flex items-center justify-between px-4 py-2 bg-gray-800 dark:bg-gray-900 border-b border-gray-700">
        <div className="flex items-center gap-2">
          <Code className="h-4 w-4 text-gray-400" />
          <span className="text-sm font-medium text-gray-300">
            {codeData.filename || codeData.language || 'Code'}
          </span>
          {codeData.language && (
            <span className="text-xs px-2 py-1 bg-gray-700 text-gray-300 rounded">
              {codeData.language}
            </span>
          )}
        </div>
        <Button
          variant="ghost"
          size="sm"
          onClick={copyToClipboard}
          className="h-8 w-8 p-0 text-gray-400 hover:text-gray-200"
        >
          {copied ? (
            <CheckCircle className="h-4 w-4" />
          ) : (
            <Copy className="h-4 w-4" />
          )}
        </Button>
      </div>
      <div className="p-4 overflow-x-auto">
        <pre className="text-sm text-gray-100 font-mono leading-relaxed">
          <code>{codeData.code}</code>
        </pre>
      </div>
    </div>
  );
};

// Component for rendering file attachments
const FileAttachmentRenderer: React.FC<{ fileData: ChatMessage['file_data'] }> = ({ fileData }) => {
  if (!fileData) return null;

  const getFileIcon = (type: string) => {
    switch (type) {
      case 'code':
        return <Code className="h-5 w-5 text-blue-500" />;
      case 'document':
        return <FileText className="h-5 w-5 text-green-500" />;
      case 'image':
        return <Eye className="h-5 w-5 text-purple-500" />;
      default:
        return <FileText className="h-5 w-5 text-gray-500" />;
    }
  };

  return (
    <div className="flex items-center gap-3 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
      <div className="flex-shrink-0">
        {getFileIcon(fileData.type)}
      </div>
      <div className="flex-1 min-w-0">
        <p className="text-sm font-medium text-gray-900 dark:text-gray-100 truncate">
          {fileData.filename}
        </p>
        <p className="text-xs text-gray-500 dark:text-gray-400">
          {fileData.type} • {fileData.size}
        </p>
      </div>
      <div className="flex-shrink-0 flex gap-2">
        {fileData.url && (
          <Button
            variant="ghost"
            size="sm"
            onClick={() => window.open(fileData.url, '_blank')}
            className="h-8 w-8 p-0 text-gray-400 hover:text-gray-600"
          >
            <Eye className="h-4 w-4" />
          </Button>
        )}
        <Button
          variant="ghost"
          size="sm"
          onClick={() => {
            // Handle download logic here
            console.log('Download file:', fileData.filename);
          }}
          className="h-8 w-8 p-0 text-gray-400 hover:text-gray-600"
        >
          <Download className="h-4 w-4" />
        </Button>
      </div>
    </div>
  );
};

// Enhanced message content renderer
const MessageContentRenderer: React.FC<{ message: ChatMessage }> = ({ message }) => {
  const renderContent = () => {
    switch (message.content_type) {
      case 'plan':
        return <PlanRenderer planData={message.plan_data} />;
      case 'code':
        return <CodeRenderer codeData={message.code_data} />;
      case 'file_attachment':
        return <FileAttachmentRenderer fileData={message.file_data} />;
      case 'text':
      default:
        // Check if content has structured formatting
        if (message.content.includes('\n') &&
            (message.content.match(/^\d+\.\s+/m) || message.content.match(/^[•·]\s+/m))) {
          return <FormattedTextRenderer content={message.content} />;
        }
        // Use existing MarkdownRenderer for regular content
        return (
          <MarkdownRenderer
            content={message.content}
            markdownContent={(message as any).markdown_content}
            contentType={(message as any).content_type}
            role={message.sender}
            showPreview={true}
          />
        );
    }
  };

  return (
    <div className="space-y-3">
      {renderContent()}

      {/* Show file attachments alongside other content if present */}
      {message.content_type !== 'file_attachment' && message.file_data && (
        <FileAttachmentRenderer fileData={message.file_data} />
      )}
    </div>
  );
};

export default function ManusChatMonitor() {
  const [message, setMessage] = useState('');
  const [profileName, setProfileName] = useState('');
  const [isHeadless, setIsHeadless] = useState(true);
  const [forceReset, setForceReset] = useState(false);

  // Function to add demo messages for testing different UI types
  const addDemoMessages = () => {
    const demoMessages: ChatMessage[] = [
      {
        id: Date.now() + 1,
        content: 'Tôi đã hoàn thành việc sửa đổi mã API để phù hợp với luồng công việc của mã kiểm tra. Dưới đây là tóm tắt những thay đổi chính:\n\n1. Đơn giản hóa quy trình đăng nhập Google:\n\n• Loại bỏ các tiếp cận phức tạp với nhiều bộ chọn (selectors)\n• Sử dụng hàm `try_google_login()` đơn giản hơn từ mã kiểm tra\n• Loại bỏ ảnh chụp màn hình gỡ lỗi và các vòng lặp bộ chọn không cần thiết\n\n2. Tối ưu hóa xử lý lỗi:\n\n• Đơn giản hóa xử lý lỗi để phù hợp với cách tiếp cận của mã kiểm tra\n• Duy trì thông báo lỗi rõ ràng trong đối tượng kết quả\n• Loại bỏ các đường dẫn kiểm tra lỗi dư thừa',
        sender: 'assistant',
        timestamp: new Date().toISOString(),
        content_type: 'text',
        status: 'sent'
      },
      {
        id: Date.now() + 2,
        content: 'I\'ll help you with modifying the API code to work like the test code. Let me first examine both code files you\'ve provided to understand what changes are needed.',
        sender: 'assistant',
        timestamp: new Date().toISOString(),
        content_type: 'plan',
        plan_data: {
          steps: [
            {
              title: 'Read and analyze the test code.',
              description: 'Reviewing and preparing to compare the provided code files.',
              status: 'completed',
              substeps: [
                { title: 'Reading file upload/pasted_content.txt', status: 'completed' },
                { title: 'Reading and analyzing the test code file.', status: 'completed' }
              ]
            },
            {
              title: 'Read and analyze the API code.',
              description: 'Reading and analyzing the API code file.',
              status: 'completed',
              substeps: [
                { title: 'Reading file upload/pasted_content_2.txt', status: 'completed' }
              ]
            },
            {
              title: 'Identify required changes for the workflow.',
              description: 'Identifying required changes for workflow alignment.',
              status: 'in_progress'
            },
            {
              title: 'Modify the API code to match the test workflow.',
              description: 'Modifying the API code to match the test code workflow.',
              status: 'pending'
            }
          ]
        },
        status: 'sent'
      },
      {
        id: Date.now() + 3,
        content: 'Đối với profile đã được xác thực, hãy sử dụng tham số `skip_login_check`:',
        sender: 'assistant',
        timestamp: new Date().toISOString(),
        content_type: 'code',
        code_data: {
          language: 'bash',
          code: `curl -X POST "http://localhost:8800/manus-chat-profile" \\
  -H "Content-Type: application/json" \\
  -d '{
    "chat_content": "Xin chào từ API test curl",
    "profile_name": "my_profile",
    "headless": false,
    "wait_time": 3,
    "skip_login_check": true
  }'`,
          filename: 'api_test.sh'
        },
        status: 'sent'
      },
      {
        id: 'demo-4',
        content: 'Mã mới cung cấp trải nghiệm xác thực liền mạch mà không yêu cầu bất kỳ tham số đặc biệt nào. Nó thông minh hơn con đường trực tiếp nhất (truy cập trang chính) và chỉ quay lại các phương thức xác thực phức tạp hơn khi cần thiết.',
        sender: 'assistant',
        timestamp: new Date().toISOString(),
        content_type: 'file_attachment',
        file_data: {
          filename: 'refactored_api_code.py',
          size: '35.86 KB',
          type: 'code',
          url: '#'
        },
        status: 'sent'
      }
    ];

    setMessages(prev => [...prev, ...demoMessages]);
  };
  const [wsStatus, setWsStatus] = useState<any>(null);
  const [showRemoteControl, setShowRemoteControl] = useState(false);
  const [status, setStatus] = useState({
    status: 'idle',
    message: '',
    progress: 0,
    response: null
  });
  const [vncVisible, setVncVisible] = useState(false);
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Sử dụng WebSocket context
  const {
    isConnected,
    status: wsContextStatus,
    formattedElapsedTime,
    connect,
    sendMessage,
    chatHistory,
    addMessageToHistory,
    realtimeData,
    monitoringActive,
    chatContext
  } = useWebSocket();

  // Function to scroll to the bottom of the chat
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  // Effect to scroll to bottom when messages change
  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // Effect to update messages when chatHistory changes - với append logic và unique keys
  useEffect(() => {
    if (chatHistory && chatHistory.length > 0) {
      console.log("Updating messages from chat history:", chatHistory);

      const formattedMessages = chatHistory
        .filter((msg): msg is Required<Pick<typeof msg, 'content'>> & typeof msg =>
          msg.content != null
        )
        .map((msg, index) => ({
          ...msg,
          id:
            msg.id ??
            `${msg.timestamp ?? Date.now()}_${index}_${
              msg.content.slice(0, 10).replace(/\s/g, '') || 'empty'
            }`,
          status: 'sent' as const,
          content: msg.content,
        }));

      // Append logic: chỉ thêm tin nhắn mới, không replace toàn bộ
      setMessages(prevMessages => {
        // Nếu chưa có tin nhắn nào, set toàn bộ
        if (prevMessages.length === 0) {
          return formattedMessages;
        }

        // Tạo Set của các IDs hiện có để kiểm tra duplicate
        const existingIds = new Set(prevMessages.map(msg => msg.id));

        // Lọc ra chỉ những tin nhắn mới (không có ID trùng lặp)
        const newMessages = formattedMessages.filter(msg => !existingIds.has(msg.id));

        if (newMessages.length > 0) {
          console.log(`Appending ${newMessages.length} new messages`);
          return [...prevMessages, ...newMessages];
        }

        // Nếu không có tin nhắn mới, giữ nguyên
        return prevMessages;
      });

      // Đảm bảo cuộn xuống tin nhắn mới nhất
      setTimeout(() => {
        scrollToBottom();
      }, 100);
    }
  }, [chatHistory]);

  // Effect to handle completion status from WebSocket context
  useEffect(() => {
    // Get status from WebSocket context
    if (wsContextStatus && wsContextStatus.response) {
      console.log("Detected response from WebSocket:", wsContextStatus.response);

      // Kiểm tra nếu là thông báo hoàn thành
      const responseText = wsContextStatus.response as string;
      if (responseText === "Manus has completed the current task") {
        console.log("Phát hiện thông báo hoàn thành");
        // Cập nhật trạng thái thành công
        setStatus({
          status: 'success',
          message: 'Manus đã hoàn thành nhiệm vụ!',
          progress: 100,
          response: responseText
        });
        return;
      }
    }
  }, [wsContextStatus]);

  // Removed duplicate logic - messages are now handled only through chatHistory

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (!message) {
      alert('Vui lòng nhập nội dung tin nhắn');
      return;
    }

    // Không cần thêm tin nhắn người dùng vào lịch sử ở đây
    // vì sendMessage sẽ tự động thêm tin nhắn vào lịch sử

    // Clear the input field
    setMessage('');

    // Cập nhật trạng thái UI
    setStatus({
      status: 'processing',
      message: 'Đang chuẩn bị kết nối...',
      progress: 5,
      response: null
    });

    if (!isConnected) {
      // Kết nối WebSocket trước
      connect();

      // Đợi kết nối được thiết lập trước khi gửi dữ liệu
      const connectionTimeout = setTimeout(() => {
        setStatus({
          status: 'error',
          message: 'Không thể kết nối đến WebSocket sau 5 giây. Vui lòng thử lại.',
          progress: 0,
          response: null
        });
      }, 5000);

      // Kiểm tra kết nối mỗi 100ms
      const checkInterval = setInterval(() => {
        if (isConnected) {
          clearTimeout(connectionTimeout);
          clearInterval(checkInterval);

          setStatus({
            status: 'processing',
            message: 'Đã kết nối, đang gửi yêu cầu...',
            progress: 10,
            response: null
          });

          // Gửi tin nhắn khi đã kết nối
          const sent = sendMessage(message, true, profileName);

          if (!sent) {
            setStatus({
              status: 'error',
              message: 'Không thể gửi dữ liệu đến WebSocket. Vui lòng thử lại.',
              progress: 0,
              response: null
            });
          }
        }
      }, 100);

      // Dừng kiểm tra sau 5 giây nếu không kết nối được
      setTimeout(() => {
        clearInterval(checkInterval);
      }, 5000);
    } else {
      // Đã kết nối, gửi tin nhắn ngay
      const sent = sendMessage(message, true, profileName);

      if (!sent) {
        setStatus({
          status: 'error',
          message: 'Không thể gửi dữ liệu đến WebSocket. Vui lòng thử lại.',
          progress: 0,
          response: null
        });
      }
    }
  };

  const getStatusIcon = () => {
    switch (status.status) {
      case 'success':
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case 'error':
        return <XCircle className="h-5 w-5 text-red-500" />;
      case 'warning':
        return <AlertCircle className="h-5 w-5 text-amber-500" />;
      case 'processing':
        return <RefreshCcw className="h-5 w-5 text-blue-500 animate-spin" />;
      default:
        return null;
    }
  };

  const createProfile = async () => {
    if (!profileName) {
      alert("Vui lòng nhập tên profile");
      return;
    }

    setStatus({
      status: 'processing',
      message: 'Đang tạo profile Chrome...',
      progress: 10,
      response: null
    });

    try {
      console.log("Đang gửi yêu cầu tạo profile...", profileName);

      const response = await fetch('http://localhost:8000/setup-chrome-profile', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          profile_name: profileName,
          headless: false
        }),
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error("Lỗi HTTP:", response.status, errorText);
        throw new Error(`HTTP error! status: ${response.status}, body: ${errorText}`);
      }

      const result = await response.json();
      console.log("Kết quả tạo profile:", result);

      if (result.status === "success") {
        const displayPath = result.profile_path.replace('/root/chrome_profiles', './chrome_profiles');
        setStatus({
          status: 'success',
          message: `Đã tạo profile thành công: ${displayPath}`,
          progress: 100,
          response: null
        });
      } else {
        setStatus({
          status: 'error',
          message: result.message || "Không thể tạo profile",
          progress: 0,
          response: null
        });
      }
    } catch (error: any) {
      console.error('Lỗi chi tiết khi tạo profile:', error);
      setStatus({
        status: 'error',
        message: `Không thể tạo profile: ${error.message || 'Lỗi kết nối'}`,
        progress: 0,
        response: null
      });
    }
  };

  // WebSocket riêng cho thiết lập profile
  const [profileWs, setProfileWs] = useState<WebSocket | null>(null);
  const [profileSetupStatus, setProfileSetupStatus] = useState({
    status: 'idle',
    message: '',
    progress: 0,
    completed: false,
    response: null
  });

  // Hàm khởi tạo WebSocket cho thiết lập profile
  const connectProfileWebSocket = () => {
    // Đóng kết nối cũ nếu có
    if (profileWs) {
      profileWs.close();
    }

    try {
      // Lấy hostname từ window.location
      const wsHostname = typeof window !== 'undefined'
        ? window.location.hostname
        : 'localhost';

      // Tạo WebSocket URL
      const wsUrl = `ws://${wsHostname}:8000/ws/profile-setup`;

      console.log('Đang kết nối đến WebSocket URL cho thiết lập profile:', wsUrl);
      const socket = new WebSocket(wsUrl);

      socket.onopen = () => {
        console.log('WebSocket kết nối thành công cho thiết lập profile');
        setProfileSetupStatus(data => ({
          ...data,
          status: 'idle',
          message: 'Đã kết nối WebSocket thành công cho thiết lập profile',
          progress: 0,
          completed: false,
          response: null
        }));
      };

      socket.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);

          // Xử lý ping từ server
          if (data.type === 'ping') {
            console.debug('Nhận ping từ server, gửi lại pong');
            if (socket.readyState === WebSocket.OPEN) {
              socket.send(JSON.stringify({ type: 'pong', timestamp: data.timestamp }));
            }
            return;
          }

          console.log('Nhận cập nhật thiết lập profile:', data);

          // Cập nhật trạng thái
          setProfileSetupStatus(data);

          // Hiển thị VNC khi nhận được thông tin VNC
          if (data.vnc_details) {
            setVncVisible(true);
            setShowRemoteControl(true);
          }

          // Cập nhật trạng thái chung
          setStatus({
            status: data.status,
            message: data.message,
            progress: data.progress,
            response: data.response || JSON.stringify(data, null, 2)
          });

          // Đóng WebSocket khi hoàn thành
          if (data.completed) {
            setTimeout(() => {
              socket.close();
            }, 1000);
          }
        } catch (error) {
          console.error('Lỗi khi xử lý dữ liệu WebSocket thiết lập profile:', error);
        }
      };

      socket.onclose = (event) => {
        console.log('WebSocket thiết lập profile đã đóng:', event.code, event.reason);
        setProfileWs(null);
      };

      socket.onerror = (error) => {
        console.error('Lỗi WebSocket thiết lập profile:', error);
        setProfileSetupStatus({
          status: 'error',
          message: 'Lỗi kết nối WebSocket thiết lập profile',
          progress: 0,
          completed: false,
          response: null
        });
      };

      setProfileWs(socket);
      return socket;
    } catch (error) {
      console.error('Lỗi khi tạo kết nối WebSocket thiết lập profile:', error);
      return null;
    }
  };

  // Khởi tạo giá trị mặc định cho wsStatus nếu chưa được khởi tạo
  useEffect(() => {
    if (wsStatus === null) {
      setWsStatus({
        status: 'idle',
        message: '',
        progress: 0,
        completed: false,
        response: null
      });
    }
  }, [wsStatus, setWsStatus]);

  // Hàm gửi yêu cầu hoàn thành thiết lập profile
  const completeProfileSetup = () => {
    console.log("Đang gửi yêu cầu hoàn thành thiết lập profile...");

    if (profileWs && profileWs.readyState === WebSocket.OPEN) {
      // Gửi yêu cầu hoàn thành qua WebSocket
      console.log("Gửi yêu cầu hoàn thành qua WebSocket");
      try {
        profileWs.send(JSON.stringify({
          action: 'complete',
          profile_name: profileName
        }));

        // Cập nhật trạng thái
        setStatus({
          status: 'success',
          message: 'Đã hoàn thành thiết lập profile',
          progress: 100,
          response: null
        });

        // Đặt trạng thái WebSocket để hiển thị thông báo hoàn thành
        setWsStatus({
          status: 'success',
          message: 'Đã hoàn thành thiết lập profile',
          progress: 100,
          completed: true,
          response: null
        });
      } catch (error) {
        console.error("Lỗi khi gửi yêu cầu hoàn thành qua WebSocket:", error);
        fallbackCompleteSetup();
      }
    } else {
      console.error('Không thể gửi yêu cầu hoàn thành: WebSocket không kết nối');
      fallbackCompleteSetup();
    }
  };

  // Fallback sử dụng API REST nếu WebSocket không khả dụng
  const fallbackCompleteSetup = () => {
    console.log("Sử dụng API REST để hoàn thành thiết lập");
    fetch('http://localhost:8000/complete-setup', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        profile_name: profileName,
        status: 'completed'
      })
    }).then(response => response.json())
      .then(data => {
        console.log("Kết quả API hoàn thành:", data);
        // Cập nhật trạng thái
        setStatus({
          status: 'success',
          message: 'Đã hoàn thành thiết lập profile',
          progress: 100,
          response: JSON.stringify(data)
        });

        // Đặt trạng thái WebSocket để hiển thị thông báo hoàn thành
        setWsStatus({
          status: 'success',
          message: 'Đã hoàn thành thiết lập profile',
          progress: 100,
          completed: true,
          response: null
        });
      })
      .catch(error => {
        console.error("Lỗi khi hoàn thành:", error);
        setStatus({
          status: 'error',
          message: `Lỗi khi hoàn thành thiết lập: ${error.message || 'Không xác định'}`,
          progress: 0,
          response: null
        });
      });
  };

  // Hàm mở trình duyệt để đăng nhập
  const openBrowserToLogin = async () => {
    // Kiểm tra tên profile
    if (!profileName) {
      alert("Vui lòng nhập tên profile");
      return;
    }

    // Cập nhật trạng thái
    setStatus({
      status: 'processing',
      message: 'Đang chuẩn bị thiết lập Chrome profile...',
      progress: 5,
      response: null
    });

    try {
      // Kết nối WebSocket
      const socket = connectProfileWebSocket();

      if (!socket) {
        setStatus({
          status: 'error',
          message: 'Không thể kết nối WebSocket để thiết lập profile',
          progress: 0,
          response: null
        });
        return;
      }

      // Đợi kết nối được thiết lập
      const checkConnection = () => {
        console.log("Kiểm tra trạng thái kết nối:", socket.readyState);

        if (socket.readyState === WebSocket.OPEN) {
          // Gửi yêu cầu thiết lập profile
          console.log("Gửi yêu cầu thiết lập profile:", profileName);
          socket.send(JSON.stringify({
            profile_name: profileName,
            headless: false,
            url: "https://accounts.google.com",
            use_stealth: true,
            force_reset: forceReset
          }));
        } else if (socket.readyState === WebSocket.CONNECTING) {
          // Vẫn đang kết nối, đợi thêm
          setTimeout(checkConnection, 500);
        } else {
          // Kết nối thất bại
          setStatus({
            status: 'error',
            message: 'Không thể kết nối WebSocket để thiết lập profile',
            progress: 0,
            response: null
          });
        }
      };

      // Bắt đầu kiểm tra kết nối
      checkConnection();

    } catch (error) {
      console.error("Lỗi khi thiết lập kết nối WebSocket:", error);
      setStatus({
        status: 'error',
        message: `Lỗi khi thiết lập kết nối: ${error.message || 'Không xác định'}`,
        progress: 0,
        response: null
      });
    }
  };

  return (
    <div className="flex flex-col space-y-6 w-full max-w-4xl mx-auto p-4">
      <Card className="flex flex-col h-[700px]">
        <CardHeader>
          <CardTitle>Manus AI Chat</CardTitle>
          <CardDescription>
            Chat with Manus AI and get real-time responses
          </CardDescription>
        </CardHeader>
        <CardContent className="flex-1 flex flex-col">
          {/* Settings Panel */}
          <div className="mb-4 p-3 bg-gray-50 dark:bg-gray-900 rounded-md">
            <div className="flex flex-col space-y-2">
              <div className="flex items-center space-x-2">
                <Label htmlFor="profile-name" className="w-32">Chrome Profile:</Label>
                <Input
                  id="profile-name"
                  value={profileName}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) => setProfileName(e.target.value)}
                  placeholder="Enter Chrome profile name"
                  disabled={status.status === 'processing'}
                  className="flex-1"
                />
              </div>
              <div className="flex items-center space-x-2">
                <Label htmlFor="headless-mode" className="w-32">Headless Mode:</Label>
                <Switch
                  id="headless-mode"
                  checked={isHeadless}
                  onCheckedChange={setIsHeadless}
                  disabled={status.status === 'processing'}
                />
                <span className="text-sm text-gray-500">
                  {isHeadless ? "Browser will run in background" : "Browser will be visible"}
                </span>
              </div>

              <div className="flex items-center space-x-2">
                <Label htmlFor="force-reset" className="w-32">Force Reset:</Label>
                <Switch
                  id="force-reset"
                  checked={forceReset}
                  onCheckedChange={setForceReset}
                  disabled={status.status === 'processing'}
                />
                <span className="text-sm text-gray-500">
                  {forceReset ? "Will reset profile if it exists" : "Will use existing profile"}
                </span>
              </div>
              <div className="flex flex-wrap gap-2 mt-2">
                <Button onClick={createProfile} variant="outline">
                  Create Profile
                </Button>
                <Button onClick={openBrowserToLogin} variant="outline">
                  Open Browser to Login
                </Button>
                <Button onClick={addDemoMessages} variant="outline">
                  Add Demo Messages
                </Button>
                <Button
                  onClick={() => {
                    setShowRemoteControl(!showRemoteControl);
                    if (!showRemoteControl) {
                      setVncVisible(true);
                    }
                  }}
                  variant={showRemoteControl ? "default" : "outline"}
                >
                  {showRemoteControl ? "Hide Remote Control" : "Show Remote Control"}
                </Button>
                <Button
                  onClick={() => {
                    setStatus({
                      status: 'processing',
                      message: 'Restarting browser...',
                      progress: 10,
                      response: null
                    });
                    fetch('http://localhost:8000/restart-vnc-service', {
                      method: 'POST',
                      headers: { 'Content-Type': 'application/json' }
                    }).then(response => response.json())
                      .then(data => {
                        console.log("Restart response:", data);
                        setStatus({
                          status: 'success',
                          message: 'Browser restarted successfully. Try again.',
                          progress: 100,
                          response: null
                        });
                        setVncVisible(true);
                        setShowRemoteControl(true);
                      })
                      .catch(error => {
                        console.error("Error restarting browser:", error);
                        setStatus({
                          status: 'error',
                          message: `Error restarting browser: ${error.message}`,
                          progress: 0,
                          response: null
                        });
                      });
                  }}
                  variant="outline"
                >
                  Restart Browser
                </Button>
              </div>
            </div>
          </div>

          {/* Chat Messages Area - Manus-inspired design */}
          <ScrollArea className="flex-1 pr-4 mb-4 border rounded-md bg-[var(--background-white-main,#ffffff)] dark:bg-[var(--background-dark-main,#1a1a1a)]">
            <div className="p-4 space-y-3">
              {messages.length === 0 ? (
                <div className="flex flex-col items-center justify-center h-40 text-gray-400">
                  <div className="h-12 w-12 rounded-full bg-gray-100 dark:bg-gray-800 flex items-center justify-center mb-3">
                    <Bot size={24} className="text-gray-500" />
                  </div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Start a conversation with Manus AI</p>
                  <p className="text-xs text-gray-500 dark:text-gray-500 mt-1">Send a message to begin real-time monitoring</p>
                  {status.status === 'processing' && (
                    <div className="mt-4 flex items-center text-blue-600">
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      <span className="text-sm">{status.message}</span>
                    </div>
                  )}
                </div>
              ) : (
                <>
                  {messages.map((msg: ChatMessage) => (
                    <div
                      key={msg.id}
                      className={`flex gap-3 ${msg.sender === 'user' ? 'justify-end' : 'justify-start'} mb-3`}
                    >
                      {/* Avatar for assistant messages */}
                      {msg.sender === 'assistant' && (
                        <div className="flex-shrink-0">
                          <div className="h-8 w-8 rounded-full bg-teal-600 flex items-center justify-center">
                            <Bot className="h-4 w-4 text-white" />
                          </div>
                        </div>
                      )}

                      <div className={`max-w-[80%] ${msg.sender === 'user' ? 'ml-auto' : 'mr-auto'}`}>
                        {msg.sender === 'user' ? (
                          // User message - Manus-inspired design
                          <div className="bg-[var(--primary,#0ea5e9)] text-white rounded-lg px-4 py-3 shadow-sm">
                            <div className="whitespace-pre-wrap break-words text-sm leading-relaxed">
                              {msg.content}
                            </div>
                            <div className="flex items-center justify-end mt-2 gap-2">
                              <span className="text-xs opacity-70">
                                {msg.timestamp || new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                              </span>
                              {msg.status === 'sending' && (
                                <Loader2 className="h-3 w-3 animate-spin opacity-70" />
                              )}
                            </div>
                          </div>
                        ) : (
                          // Assistant message - Enhanced design with new content renderer
                          <div className="bg-white dark:bg-gray-800 rounded-lg px-4 py-3 shadow-sm border border-gray-200 dark:border-gray-700">
                            <div className="prose prose-sm max-w-none dark:prose-invert">
                              <MessageContentRenderer message={msg} />
                            </div>
                            <div className="flex items-center justify-between mt-3 pt-2 border-t border-gray-100 dark:border-gray-700">
                              <div className="flex items-center gap-2">
                                <span className="text-xs text-gray-500 dark:text-gray-400">
                                  {msg.timestamp || new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                                </span>
                                {(msg as any).metadata?.completion_status && (
                                  <div className="px-2 py-1 rounded-full bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300 text-xs font-medium">
                                    ✓ Completed
                                  </div>
                                )}
                                {/* Show content type indicator */}
                                {msg.content_type && msg.content_type !== 'text' && (
                                  <div className="px-2 py-1 rounded-full bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 text-xs font-medium capitalize">
                                    {msg.content_type.replace('_', ' ')}
                                  </div>
                                )}
                              </div>
                              <div className="flex items-center gap-1">
                                <Button variant="ghost" size="sm" className="h-6 w-6 p-0 text-gray-400 hover:text-gray-600">
                                  <svg className="h-3 w-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                                  </svg>
                                </Button>
                              </div>
                            </div>
                          </div>
                        )}
                      </div>

                      {/* Avatar for user messages */}
                      {msg.sender === 'user' && (
                        <div className="flex-shrink-0">
                          <div className="h-8 w-8 rounded-full bg-gray-300 dark:bg-gray-700 flex items-center justify-center">
                            <User className="h-4 w-4 text-gray-600 dark:text-gray-300" />
                          </div>
                        </div>
                      )}
                    </div>
                  ))}

                  {/* Show processing indicator when monitoring is active - Enhanced design */}
                  {monitoringActive && (
                    <div className="flex gap-3 justify-start mb-3">
                      <div className="flex-shrink-0">
                        <div className="h-8 w-8 rounded-full bg-teal-600 flex items-center justify-center">
                          <Bot className="h-4 w-4 text-white" />
                        </div>
                      </div>
                      <div className="max-w-[80%] mr-auto">
                        <div className="bg-white dark:bg-gray-800 rounded-lg px-4 py-3 shadow-sm border border-gray-200 dark:border-gray-700">
                          <div className="flex items-center gap-2">
                            <div className="flex gap-1">
                              <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0ms' }}></div>
                              <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '150ms' }}></div>
                              <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '300ms' }}></div>
                            </div>
                            <span className="text-sm text-gray-600 dark:text-gray-400 ml-2">
                              Manus is thinking...
                            </span>
                          </div>
                          <div className="flex items-center justify-between mt-2 pt-2 border-t border-gray-100 dark:border-gray-700">
                            <span className="text-xs text-gray-500 dark:text-gray-400">
                              {new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                            </span>
                            <div className="px-2 py-1 rounded-full bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 text-xs font-medium">
                              Processing...
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}
                </>
              )}
              <div ref={messagesEndRef} />
            </div>
          </ScrollArea>

          {/* Message Input - Manus-inspired design */}
          <div className="space-y-3">
            <form onSubmit={handleSubmit} className="relative">
              <div className="relative flex items-end gap-2 p-3 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-sm focus-within:ring-2 focus-within:ring-blue-500 focus-within:border-blue-500">
                <div className="flex-1 min-h-[40px] max-h-[120px] overflow-y-auto">
                  <textarea
                    value={message}
                    onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) => setMessage(e.target.value)}
                    placeholder={messages.length === 0 ? "Give Manus a task to work on..." : "Send message to Manus"}
                    disabled={status.status === 'processing' && !status.response}
                    className="w-full resize-none border-0 bg-transparent text-sm placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-0"
                    rows={1}
                    style={{
                      minHeight: '24px',
                      height: 'auto'
                    }}
                    onInput={(e) => {
                      const target = e.target as HTMLTextAreaElement;
                      target.style.height = 'auto';
                      target.style.height = Math.min(target.scrollHeight, 120) + 'px';
                    }}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter' && !e.shiftKey) {
                        e.preventDefault();
                        handleSubmit(e as any);
                      }
                    }}
                  />
                </div>

                <div className="flex items-center gap-2">
                  <Button
                    type="submit"
                    size="sm"
                    disabled={status.status === 'processing' && !status.response || !message.trim()}
                    className="h-8 w-8 p-0 rounded-full bg-blue-600 hover:bg-blue-700 disabled:bg-gray-300 dark:disabled:bg-gray-600"
                  >
                    <Send className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </form>

            {/* Action buttons */}
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                {messages.length > 0 && (
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      setMessages([]);
                      // clearChatHistory();
                    }}
                    disabled={status.status === 'processing'}
                    className="text-xs"
                  >
                    <RefreshCcw className="h-3 w-3 mr-1" />
                    Clear Chat
                  </Button>
                )}
              </div>

              <div className="text-xs text-gray-500 dark:text-gray-400">
                Press Enter to send, Shift+Enter for new line
              </div>
            </div>
          </div>

          {/* Connection Status */}
          <div className="mt-2 text-xs text-gray-500 flex items-center gap-2">
            <div className="flex items-center gap-1">
              <div className={`w-2 h-2 rounded-full ${isConnected ? 'bg-green-500' : 'bg-red-500'}`}></div>
              {isConnected ? 'Connected to Manus AI' : 'Disconnected from Manus AI'}
            </div>

            {monitoringActive && (
              <div className="flex items-center gap-1">
                <div className="w-2 h-2 rounded-full bg-blue-500 animate-pulse"></div>
                <span className="text-blue-600">Real-time monitoring active</span>
              </div>
            )}

            {status.status === 'success' && status.response === "Manus has completed the current task" && (
              <div className="flex items-center gap-1">
                <CheckCircle className="h-3 w-3 text-green-500" />
                <span className="text-green-600">Task completed</span>
              </div>
            )}

            {status.status === 'processing' && (
              <div className="ml-auto flex items-center gap-1">
                <Loader2 className="h-3 w-3 animate-spin" />
                <span>{formattedElapsedTime()}</span>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Real-time Data Panel */}
      {(monitoringActive || realtimeData || chatContext) && (
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg flex items-center gap-2">
              <div className={`w-3 h-3 rounded-full ${monitoringActive ? 'bg-blue-500 animate-pulse' : 'bg-gray-400'}`}></div>
              Real-time Manus Data
            </CardTitle>
            <CardDescription>
              Live data crawled from Manus.im interface
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {/* Monitoring Status */}
              {monitoringActive && (
                <div className="p-3 bg-blue-50 dark:bg-blue-900/20 rounded-md">
                  <div className="flex items-center gap-2">
                    <Loader2 className="h-4 w-4 animate-spin text-blue-600" />
                    <span className="font-medium text-blue-800 dark:text-blue-200">
                      Monitoring Active
                    </span>
                  </div>
                  <p className="text-sm text-blue-600 dark:text-blue-300 mt-1">
                    Continuously monitoring Manus interface for new responses and task completion
                  </p>
                </div>
              )}

              {/* Real-time Update Info */}
              {realtimeData && (
                <div className="p-3 bg-green-50 dark:bg-green-900/20 rounded-md">
                  <h4 className="font-medium text-green-800 dark:text-green-200 mb-2">
                    Latest Update
                  </h4>
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="text-gray-600 dark:text-gray-400">Elapsed Time:</span>
                      <span className="ml-2 font-mono">
                        {Math.floor(realtimeData.elapsed_time / 60)}:
                        {String(Math.floor(realtimeData.elapsed_time % 60)).padStart(2, '0')}
                      </span>
                    </div>
                    <div>
                      <span className="text-gray-600 dark:text-gray-400">New Messages:</span>
                      <span className="ml-2 font-semibold text-green-600">
                        {realtimeData.new_messages}
                      </span>
                    </div>
                    <div>
                      <span className="text-gray-600 dark:text-gray-400">Status:</span>
                      <span className="ml-2 capitalize">
                        {realtimeData.monitoring_status}
                      </span>
                    </div>
                    <div>
                      <span className="text-gray-600 dark:text-gray-400">Total Messages:</span>
                      <span className="ml-2 font-semibold">
                        {realtimeData.chat_context?.messages?.length || 0}
                      </span>
                    </div>
                  </div>
                </div>
              )}

              {/* Chat Context Info */}
              {chatContext && (
                <div className="p-3 bg-gray-50 dark:bg-gray-900 rounded-md">
                  <h4 className="font-medium text-gray-800 dark:text-gray-200 mb-2">
                    Chat Context
                  </h4>
                  <div className="space-y-2 text-sm">
                    <div>
                      <span className="text-gray-600 dark:text-gray-400">Page URL:</span>
                      <span className="ml-2 font-mono text-xs break-all">
                        {chatContext.page_url}
                      </span>
                    </div>
                    <div>
                      <span className="text-gray-600 dark:text-gray-400">Page Title:</span>
                      <span className="ml-2">
                        {chatContext.page_title}
                      </span>
                    </div>
                    <div>
                      <span className="text-gray-600 dark:text-gray-400">Extracted At:</span>
                      <span className="ml-2 font-mono text-xs">
                        {new Date(chatContext.extracted_at).toLocaleString()}
                      </span>
                    </div>
                    <div>
                      <span className="text-gray-600 dark:text-gray-400">Completion Status:</span>
                      <span className={`ml-2 font-semibold ${chatContext.completion_status ? 'text-green-600' : 'text-yellow-600'}`}>
                        {chatContext.completion_status ? 'Completed' : 'In Progress'}
                      </span>
                    </div>
                    {chatContext.active_elements && chatContext.active_elements.length > 0 && (
                      <div>
                        <span className="text-gray-600 dark:text-gray-400">Active Elements:</span>
                        <span className="ml-2 text-blue-600">
                          {chatContext.active_elements.length} detected
                        </span>
                      </div>
                    )}
                  </div>
                </div>
              )}

              {/* HTML Data Size Info */}
              {chatContext && (
                <div className="p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded-md">
                  <h4 className="font-medium text-yellow-800 dark:text-yellow-200 mb-2">
                    Crawled Data Size
                  </h4>
                  <div className="grid grid-cols-3 gap-4 text-sm">
                    <div>
                      <span className="text-gray-600 dark:text-gray-400">Chat HTML:</span>
                      <span className="ml-2 font-mono">
                        {chatContext.chat_container_html ?
                          `${Math.round(chatContext.chat_container_html.length / 1024)}KB` :
                          'N/A'
                        }
                      </span>
                    </div>
                    <div>
                      <span className="text-gray-600 dark:text-gray-400">Sidebar HTML:</span>
                      <span className="ml-2 font-mono">
                        {chatContext.sidebar_html ?
                          `${Math.round(chatContext.sidebar_html.length / 1024)}KB` :
                          'N/A'
                        }
                      </span>
                    </div>
                    <div>
                      <span className="text-gray-600 dark:text-gray-400">Full Page:</span>
                      <span className="ml-2 font-mono">
                        {chatContext.full_page_html ?
                          `${Math.round(chatContext.full_page_html.length / 1024)}KB` :
                          'N/A'
                        }
                      </span>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Remote Control Panel */}
      {showRemoteControl && vncVisible && (
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg">Remote Browser Control</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="border rounded-md overflow-hidden">
              <div className="bg-zinc-100 dark:bg-zinc-800 p-2 text-sm font-medium flex justify-between items-center">
                <span>VNC Remote Control</span>
                <div className="flex space-x-2">
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => {
                      fetch('http://localhost:8000/restart-vnc-service', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' }
                      }).then(() => {
                        setStatus({
                          status: 'processing',
                          message: 'Restarting VNC, please wait...',
                          progress: 50,
                          response: null
                        });
                        setTimeout(() => {
                          const iframe = document.getElementById('vnc-frame') as HTMLIFrameElement;
                          if (iframe) {
                            iframe.src = iframe.src;
                          }
                          setStatus({
                            status: 'success',
                            message: 'VNC restarted successfully',
                            progress: 100,
                            response: null
                          });
                        }, 5000);
                      }).catch(error => {
                        setStatus({
                          status: 'error',
                          message: `Error restarting VNC: ${error.message}`,
                          progress: 0,
                          response: null
                        });
                      });
                    }}
                  >
                    Restart VNC
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => {
                      window.open('http://localhost:6080/vnc.html?autoconnect=true&resize=scale&reconnect=true&password=playwright', '_blank');
                    }}
                  >
                    Open in New Tab
                  </Button>
                  <Button
                    size="sm"
                    variant="default"
                    onClick={completeProfileSetup}
                  >
                    Done
                  </Button>
                </div>
              </div>
              <div className="h-[400px] w-full relative">
                <iframe
                  id="vnc-frame"
                  src="http://localhost:6080/vnc.html?autoconnect=true&resize=scale&reconnect=true"
                  className="w-full h-full border-0"
                  title="VNC Remote Control"
                  allow="fullscreen"
                ></iframe>
                <div className="absolute bottom-2 right-2 p-2 bg-black/50 text-white text-xs rounded">
                  Default password: playwright
                </div>
              </div>
              <div className="p-2 text-xs border-t">
                <p className="mb-1 font-medium">Troubleshooting connection issues:</p>
                <ol className="list-decimal pl-4 space-y-1">
                  <li>Click "Restart VNC" and wait 5 seconds</li>
                  <li>If still not connecting, click "Open in New Tab"</li>
                  <li>If the above methods don't work, restart the container:</li>
                  <code className="bg-zinc-100 dark:bg-zinc-800 px-1 py-0.5 rounded text-xs">
                    docker restart youhome-fastapi
                  </code>
                </ol>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Status Panel - Show for both error and success */}
      {status.status === 'error' && (
        <Card className="border-red-500">
          <CardHeader className="pb-2">
            <div className="flex justify-between items-center">
              <CardTitle className="flex items-center gap-2">
                <XCircle className="h-5 w-5 text-red-500" />
                Error
              </CardTitle>
            </div>
          </CardHeader>
          <CardContent>
            <Alert variant="destructive">
              <AlertTitle>Error</AlertTitle>
              <AlertDescription>{status.message}</AlertDescription>
            </Alert>
          </CardContent>
        </Card>
      )}

      {/* Success notification when Manus completes a task - Enhanced design */}
      {status.status === 'success' && status.response === "Manus has completed the current task" && (
        <Card className="border-green-500 bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20">
          <CardHeader className="pb-3">
            <div className="flex justify-between items-center">
              <CardTitle className="flex items-center gap-2">
                <div className="h-8 w-8 rounded-full bg-green-500 flex items-center justify-center">
                  <CheckCircle className="h-5 w-5 text-white" />
                </div>
                <div>
                  <div className="text-green-800 dark:text-green-200 font-semibold">Task Completed Successfully</div>
                  <div className="text-sm text-green-600 dark:text-green-400 font-normal">Manus đã hoàn thành nhiệm vụ!</div>
                </div>
              </CardTitle>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <Alert variant="default" className="bg-white/50 dark:bg-gray-800/50 border-green-200 dark:border-green-700">
                <CheckCircle className="h-4 w-4 text-green-600" />
                <AlertTitle className="text-green-800 dark:text-green-200">Success</AlertTitle>
                <AlertDescription className="text-green-700 dark:text-green-300">
                  {status.message || "Manus has successfully completed the current task"}
                </AlertDescription>
              </Alert>

              <div className="flex items-center justify-between p-3 bg-white/70 dark:bg-gray-800/70 rounded-lg border border-green-200 dark:border-green-700">
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <span className="text-sm text-green-700 dark:text-green-300 font-medium">
                    Real-time monitoring completed
                  </span>
                </div>
                <div className="text-xs text-green-600 dark:text-green-400">
                  {new Date().toLocaleTimeString()}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}